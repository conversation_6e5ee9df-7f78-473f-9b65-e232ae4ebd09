// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                String  @id @default(cuid())
  name              String?
  email             String? @unique
  password          String
  wells             Well[]
  isAgreeTermsOfUse Boolean @default(false)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Ddr {
  id       String  @id @default(cuid())
  format   Int
  fileName String?

  dailyInformations      DailyInformation[]
  saudiDailyInformations SaudiDailyInformation[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Well {
  id                     String                  @id @default(cuid())
  name                   String
  userId                 String
  type                   Int?
  latitude               Decimal?
  longitude              Decimal?
  startDate              DateTime?
  country                String?
  dailyInformations      DailyInformation[]
  saudiDailyInformations SaudiDailyInformation[]

  user User? @relation(fields: [userId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

/// UEA
model DailyInformation {
  id                   String               @id @default(cuid())
  wellId               String
  ddrId                String
  day                  Int?
  lastCasingLinearSize Int?
  rig                  String?
  accountableParty     String?
  drillingInformation  DrillingInformation?
  activityInformation  ActivityInformation?
  events               Events[]

  well Well @relation(fields: [wellId], references: [id], onDelete: Cascade)
  ddr  Ddr  @relation(fields: [ddrId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model DrillingInformation {
  id                 String               @id @default(cuid())
  dailyInformationId String               @unique
  measureDepth       Decimal?
  totalVerticalDepth Decimal?
  rotatingHours      Decimal?
  ratePenetration    Decimal?
  formation          String?
  mud                Mud?
  parameters         DrillingParameters[]
  bottomHoleAssembly BottomHoleAssembly[]

  dailyInformation DailyInformation @relation(fields: [dailyInformationId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model ActivityInformation {
  id                 String           @id @default(cuid())
  dailyInformationId String           @unique
  npt                Int?
  summary            String?
  nextActivity       String?
  dailyInformation   DailyInformation @relation(fields: [dailyInformationId], references: [id], onDelete: Cascade)
  timelogs           Timelog[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Events {
  id                 String @id @default(cuid())
  dailyInformationId String
  type               Int

  dailyInformation DailyInformation @relation(fields: [dailyInformationId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Mud {
  id                    String   @id @default(cuid())
  drillingInformationId String   @unique
  fluidType             String?
  viscosity             Decimal?
  density               Decimal?
  plasticViscosity      Decimal?
  yieldPoint            Decimal?
  losses                Decimal?

  drillingInformation DrillingInformation @relation(fields: [drillingInformationId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model DrillingParameters {
  id                    String   @id @default(cuid())
  drillingInformationId String
  mdStart               Decimal?
  mdEnd                 Decimal?
  rop                   Decimal?
  maxRpm                Int?
  drillingTorque        Decimal?
  wob                   Decimal?

  drillingInformation DrillingInformation @relation(fields: [drillingInformationId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model BottomHoleAssembly {
  id                    String   @id @default(cuid())
  drillingInformationId String
  bhaBitName            String?
  bitSize               Int?
  bitType               String?
  bitSerialNumber       String?
  rop                   Decimal?

  drillingInformation DrillingInformation @relation(fields: [drillingInformationId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Timelog {
  id                    String   @id @default(cuid())
  activityInformationId String
  startHour             Int
  hours                 Decimal?
  phase                 String?
  opType                String?
  detail                String?

  activityInformation ActivityInformation @relation(fields: [activityInformationId], references: [id], onDelete: Cascade)

  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt
  dailyInformationId String?
}

/// ######### SAUDI ARAMCO #########
model SaudiDailyInformation {
  id        String   @id @default(cuid())
  wellId    String
  ddrId     String
  day       Int?
  rig       String?
  ddrPage   Int
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  well           Well                  @relation(fields: [wellId], references: [id])
  ddr            Ddr                   @relation(fields: [ddrId], references: [id])
  foreman        SaudiForeman?
  projectData    SaudiProjectData?
  mudData        SaudiMudData?
  mudTreatment   SaudiMudTreatment?
  miscellaneus   SaudiMiscellaneus?
  bulk           SaudiBulk?
  repair         SaudiRepair?
  bitData        SaudiBitData?
  truckBoats     SaudiTruckBoats?
  drillString    SaudiDrillString?
  personnel      SaudiPersonnel[]
  operation      SaudiOperation[]
  lostTimeDetail SaudiLostTimeDetail[]
  dailySurvey    SaudiDailySurvey[]
}

model SaudiForeman {
  id                 String   @id @default(cuid())
  dailyInformationId String   @unique
  remarks            String
  engineers          String
  manager            String
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt

  dailyInformation SaudiDailyInformation @relation(fields: [dailyInformationId], references: [id])
}

model SaudiProjectData {
  id                  String   @id @default(cuid())
  dailyInformationId  String   @unique
  charge              String
  wellbores           String
  thuraya             String
  rigFormatVsat       String
  last24HourOperation String
  next24HourPlan      String
  location            String
  nextLocation        String
  currentDepth        Float
  lastCsgSize         Int
  measureDepth        Float
  totalVerticalDepth  Float
  linerSize           Float
  tol                 Float
  prevDepth           Float
  daysSinceSpud       Float
  commDate            Int
  circ                Float
  footage             Float
  distanceFromDHA     Float
  createdAt           DateTime @default(now())
  updatedAt           DateTime @default(now())

  dailyInformation SaudiDailyInformation @relation(fields: [dailyInformationId], references: [id])
}

model SaudiMudData {
  id                 String   @id @default(cuid())
  dailyInformationId String   @unique
  weight             Float?
  flTemp             Float?
  funnel             Float?
  cakeHthp           Float?
  filtrateHthp       Float?
  cakeApi            Float?
  filtrateApi        Float?
  waterVol           Float?
  pv                 Float?
  oilVol             Float?
  yp                 Float?
  solidsVol          Float?
  elecStability      Float?
  sandVol            Float?
  rpm3               Float?
  rpm6               Float?
  lgs                Float?
  gelsSec            Int?
  gelsMin            Int?
  mbt                Int?
  ph                 Float?
  mudType            String?
  cappm              Float?
  clppm              Float?
  pptsSpurt          Float?
  pptTotal           Float?
  createdAt          DateTime @default(now())
  updatedAt          DateTime @default(now())

  dailyInformation SaudiDailyInformation @relation(fields: [dailyInformationId], references: [id])
}

model SaudiMudTreatment {
  id                 String   @id @default(cuid())
  dailyInformationId String   @unique
  barite             Int
  bent               Int
  nacl               Int
  caust              Int
  sodash             Int
  bctrn              Int
  polySal            Int
  hs2ScavL           Int
  xc                 Int
  micaFine           Int
  baroFibrs          Int
  bFibreSf           Int
  micaCrse           Int
  mrlbM              Int
  mrlbC              Int
  mrlbF              Int
  createdAt          DateTime @default(now())
  updatedAt          DateTime @default(now())

  dailyInformation SaudiDailyInformation @relation(fields: [dailyInformationId], references: [id])
}

model SaudiMiscellaneus {
  id                 String   @id @default(cuid())
  dailyInformationId String   @unique
  bopTest            Int
  bopDrills          Int
  wind               String
  sea                String
  wheater            String
  dslta              Float
  safetyMeeting      String
  createdAt          DateTime @default(now())
  updatedAt          DateTime @default(now())

  dailyInformation SaudiDailyInformation @relation(fields: [dailyInformationId], references: [id])
}

model SaudiPersonnel {
  id                 String   @id @default(cuid())
  dailyInformationId String
  company            String
  category           String
  numberOfPersons    Int
  onLocHours         Int
  operatingHours     Int
  createdAt          DateTime @default(now())
  updatedAt          DateTime @default(now())

  dailyInformation SaudiDailyInformation @relation(fields: [dailyInformationId], references: [id])
}

model SaudiBulk {
  id                 String   @id @default(cuid())
  dailyInformationId String   @unique
  drillWtrBbls       Int
  potWtrBbls         Int
  fuelBbls           Int
  bariteSx           Int
  bentonine          Int
  cementGSx          Int
  createdAt          DateTime @default(now())
  updatedAt          DateTime @default(now())

  dailyInformation SaudiDailyInformation @relation(fields: [dailyInformationId], references: [id])
}

model SaudiRepair {
  id                              String   @id @default(cuid())
  dailyInformationId              String   @unique
  instrumentation                 String
  other                           String
  computerCommunicationVsatIssues String
  createdAt                       DateTime @default(now())
  updatedAt                       DateTime

  dailyInformation SaudiDailyInformation @relation(fields: [dailyInformationId], references: [id])
}

model SaudiBitData {
  id                 String   @id @default(cuid())
  dailyInformationId String   @unique
  bitNumber          Int
  mdIn               Float
  mdOut              Float
  runFootage         Float
  hours              Float
  averageROP         Float
  wob                Int
  rpm                Int
  iadc               String
  size               Float
  manufacturer       String
  pressure           Float
  gmp                Int
  jetVel             Int
  dpAv               Float
  dcAv               Float
  bitHhp             Int
  iRow               Int
  oRow               Int
  dc                 String
  location           String
  bearings           String
  serialNumber       String
  type               String
  jets               String
  tfa                Float
  gauge              Int
  other              String
  poohReasons        String
  createdAt          DateTime @default(now())
  updatedAt          DateTime @default(now())

  dailyInformation SaudiDailyInformation @relation(fields: [dailyInformationId], references: [id])
}

model SaudiOperation {
  id                 String   @id @default(cuid())
  dailyInformationId String
  from               Int
  to                 Int
  lateral            String
  phase              String
  cat                String
  majorOp            String
  action             String
  object             String
  respCo             String
  holeDepthStart     Int
  holeDepthEnd       Int
  eventDepthStart    Int
  eventDepthEnd      Int
  summary            String
  createdAt          DateTime @default(now())
  updatedAt          DateTime @default(now())

  dailyInformation SaudiDailyInformation @relation(fields: [dailyInformationId], references: [id])
}

model SaudiTruckBoats {
  id                 String   @id @default(cuid())
  dailyInformationId String   @unique
  standbyTankers     String
  createdAt          DateTime @default(now())
  updatedAt          DateTime

  dailyInformation SaudiDailyInformation @relation(fields: [dailyInformationId], references: [id])
}

model SaudiDrillString {
  id                 String   @id @default(cuid())
  dailyInformationId String   @unique
  float              String
  bhaHours           Float
  stringWt           Int
  pickUp             Int
  slackOff           Int
  rotTorque          Int
  jasrSerial         Float
  jarsHours          Float
  shockSubSerial     String
  shockSubHours      Float
  mudMotorSerial     String
  mudMotorHours      Float
  createdAt          DateTime @default(now())
  updatedAt          DateTime @default(now())

  dailyInformation  SaudiDailyInformation   @relation(fields: [dailyInformationId], references: [id])
  drillStringTables SaudiDrillStringTable[]
}

model SaudiDrillStringTable {
  id            String   @id @default(cuid())
  drillStringId String
  order         Int
  component     String
  provider      String
  nominalSize   Float
  joints        Float
  odSize        Float
  idSize        Float
  length        Float
  topThread     String
  bottomThread  String
  weigth        Float
  cumWeigth     Float
  grade         String
  class         String
  serial        String
  createdAt     DateTime @default(now())
  updatedAt     DateTime @default(now())

  drillString SaudiDrillString @relation(fields: [drillStringId], references: [id])
}

model SaudiLostTimeDetail {
  id                 String   @id @default(cuid())
  dailyInformationId String
  hours              Float
  cumHours           Float
  ltId               String
  parentLtId         String
  type               String
  cause              String
  object             String
  respCo             String
  depth              Float
  summary            String
  createdAt          DateTime @default(now())
  updatedAt          DateTime @default(now())

  dailyInformation SaudiDailyInformation @relation(fields: [dailyInformationId], references: [id])
}

model SaudiDailySurvey {
  id                 String   @id @default(cuid())
  dailyInformationId String
  lateral            String
  surveyMd           Float
  angle              Float
  azimuth            Float
  clength            Float
  tvd                Int
  nsCoordinate       Float
  ewCoordinate       Float
  verticalSec        Float
  dls                Float
  createdAt          DateTime @default(now())
  updatedAt          DateTime @default(now())

  dailyInformation SaudiDailyInformation @relation(fields: [dailyInformationId], references: [id])
}
