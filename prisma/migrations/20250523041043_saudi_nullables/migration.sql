-- AlterTable
ALTER TABLE "SaudiDailySurvey" ALTER COLUMN "lateral" DROP NOT NULL,
ALTER COLUMN "surveyMd" DROP NOT NULL,
ALTER COLUMN "angle" DROP NOT NULL,
ALTER COLUMN "azimuth" DROP NOT NULL,
ALTER COLUMN "clength" DROP NOT NULL,
ALTER COLUMN "tvd" DROP NOT NULL,
ALTER COLUMN "nsCoordinate" DROP NOT NULL,
ALTER COLUMN "ewCoordinate" DROP NOT NULL,
ALTER COLUMN "verticalSec" DROP NOT NULL,
ALTER COLUMN "dls" DROP NOT NULL;

-- AlterTable
ALTER TABLE "SaudiDrillString" ALTER COLUMN "float" DROP NOT NULL,
ALTER COLUMN "bhaHours" DROP NOT NULL,
ALTER COLUMN "stringWt" DROP NOT NULL,
ALTER COLUMN "pickUp" DROP NOT NULL,
ALTER COLUMN "slackOff" DROP NOT NULL,
ALTER COLUMN "rotTorque" DROP NOT NULL,
ALTER COLUMN "jasrSerial" DROP NOT NULL,
ALTER COLUMN "jarsHours" DROP NOT NULL,
ALTER COLUMN "shockSubSerial" DROP NOT NULL,
ALTER COLUMN "shockSubHours" DROP NOT NULL,
ALTER COLUMN "mudMotorSerial" DROP NOT NULL,
ALTER COLUMN "mudMotorHours" DROP NOT NULL;

-- AlterTable
ALTER TABLE "SaudiDrillStringTable" ALTER COLUMN "order" DROP NOT NULL,
ALTER COLUMN "component" DROP NOT NULL,
ALTER COLUMN "provider" DROP NOT NULL,
ALTER COLUMN "nominalSize" DROP NOT NULL,
ALTER COLUMN "joints" DROP NOT NULL,
ALTER COLUMN "odSize" DROP NOT NULL,
ALTER COLUMN "idSize" DROP NOT NULL,
ALTER COLUMN "length" DROP NOT NULL,
ALTER COLUMN "topThread" DROP NOT NULL,
ALTER COLUMN "bottomThread" DROP NOT NULL,
ALTER COLUMN "weigth" DROP NOT NULL,
ALTER COLUMN "cumWeigth" DROP NOT NULL,
ALTER COLUMN "grade" DROP NOT NULL,
ALTER COLUMN "class" DROP NOT NULL,
ALTER COLUMN "serial" DROP NOT NULL;

-- AlterTable
ALTER TABLE "SaudiForeman" ALTER COLUMN "remarks" DROP NOT NULL,
ALTER COLUMN "engineers" DROP NOT NULL,
ALTER COLUMN "manager" DROP NOT NULL;

-- AlterTable
ALTER TABLE "SaudiLostTimeDetail" ALTER COLUMN "hours" DROP NOT NULL,
ALTER COLUMN "cumHours" DROP NOT NULL,
ALTER COLUMN "ltId" DROP NOT NULL,
ALTER COLUMN "parentLtId" DROP NOT NULL,
ALTER COLUMN "type" DROP NOT NULL,
ALTER COLUMN "cause" DROP NOT NULL,
ALTER COLUMN "object" DROP NOT NULL,
ALTER COLUMN "respCo" DROP NOT NULL,
ALTER COLUMN "depth" DROP NOT NULL,
ALTER COLUMN "summary" DROP NOT NULL;

-- AlterTable
ALTER TABLE "SaudiMudTreatment" ALTER COLUMN "barite" DROP NOT NULL,
ALTER COLUMN "bent" DROP NOT NULL,
ALTER COLUMN "nacl" DROP NOT NULL,
ALTER COLUMN "caust" DROP NOT NULL,
ALTER COLUMN "sodash" DROP NOT NULL,
ALTER COLUMN "bctrn" DROP NOT NULL,
ALTER COLUMN "polySal" DROP NOT NULL,
ALTER COLUMN "hs2ScavL" DROP NOT NULL,
ALTER COLUMN "xc" DROP NOT NULL,
ALTER COLUMN "micaFine" DROP NOT NULL,
ALTER COLUMN "baroFibrs" DROP NOT NULL,
ALTER COLUMN "bFibreSf" DROP NOT NULL,
ALTER COLUMN "micaCrse" DROP NOT NULL,
ALTER COLUMN "mrlbM" DROP NOT NULL,
ALTER COLUMN "mrlbC" DROP NOT NULL,
ALTER COLUMN "mrlbF" DROP NOT NULL;

-- AlterTable
ALTER TABLE "SaudiOperation" ALTER COLUMN "from" DROP NOT NULL,
ALTER COLUMN "to" DROP NOT NULL,
ALTER COLUMN "lateral" DROP NOT NULL,
ALTER COLUMN "phase" DROP NOT NULL,
ALTER COLUMN "cat" DROP NOT NULL,
ALTER COLUMN "majorOp" DROP NOT NULL,
ALTER COLUMN "action" DROP NOT NULL,
ALTER COLUMN "object" DROP NOT NULL,
ALTER COLUMN "respCo" DROP NOT NULL,
ALTER COLUMN "holeDepthStart" DROP NOT NULL,
ALTER COLUMN "holeDepthEnd" DROP NOT NULL,
ALTER COLUMN "eventDepthStart" DROP NOT NULL,
ALTER COLUMN "eventDepthEnd" DROP NOT NULL,
ALTER COLUMN "summary" DROP NOT NULL;

-- AlterTable
ALTER TABLE "SaudiPersonnel" ALTER COLUMN "company" DROP NOT NULL,
ALTER COLUMN "category" DROP NOT NULL,
ALTER COLUMN "numberOfPersons" DROP NOT NULL,
ALTER COLUMN "onLocHours" DROP NOT NULL,
ALTER COLUMN "operatingHours" DROP NOT NULL;

-- AlterTable
ALTER TABLE "SaudiProjectData" ALTER COLUMN "charge" DROP NOT NULL,
ALTER COLUMN "wellbores" DROP NOT NULL,
ALTER COLUMN "thuraya" DROP NOT NULL,
ALTER COLUMN "rigFormatVsat" DROP NOT NULL,
ALTER COLUMN "last24HourOperation" DROP NOT NULL,
ALTER COLUMN "next24HourPlan" DROP NOT NULL,
ALTER COLUMN "location" DROP NOT NULL,
ALTER COLUMN "nextLocation" DROP NOT NULL,
ALTER COLUMN "currentDepth" DROP NOT NULL,
ALTER COLUMN "lastCsgSize" DROP NOT NULL,
ALTER COLUMN "measureDepth" DROP NOT NULL,
ALTER COLUMN "totalVerticalDepth" DROP NOT NULL,
ALTER COLUMN "linerSize" DROP NOT NULL,
ALTER COLUMN "tol" DROP NOT NULL,
ALTER COLUMN "prevDepth" DROP NOT NULL,
ALTER COLUMN "daysSinceSpud" DROP NOT NULL,
ALTER COLUMN "commDate" DROP NOT NULL,
ALTER COLUMN "circ" DROP NOT NULL,
ALTER COLUMN "footage" DROP NOT NULL,
ALTER COLUMN "distanceFromDHA" DROP NOT NULL;

-- AlterTable
ALTER TABLE "SaudiRepair" ALTER COLUMN "instrumentation" DROP NOT NULL,
ALTER COLUMN "other" DROP NOT NULL,
ALTER COLUMN "computerCommunicationVsatIssues" DROP NOT NULL;

-- AlterTable
ALTER TABLE "SaudiTruckBoats" ALTER COLUMN "standbyTankers" DROP NOT NULL,
ALTER COLUMN "updatedAt" SET DEFAULT CURRENT_TIMESTAMP;
