import { z } from "zod";

import { createTR<PERSON><PERSON>outer, protectedProcedure } from "~/server/api/trpc";
import { db } from "~/server/db";
import { dayToYYYYMMDD } from "~/utils/date/date_utils";
import { ropVsDepth } from "~/server/api/routers/well/charts/rop_vs_depth_route";
import { cumulativeNPT } from "~/server/api/routers/well/charts/cumulative_npt_route";
import { TimeVsDepth } from "~/server/api/routers/well/charts/time_vs_depth_route";
import { getUAEMapWells } from "~/server/api/routers/well/map/map_wells_route";

export const wellRouter = createTRPCRouter({
  getAllWellsForUser: protectedProcedure
    .input(
      z.object({
        userId: z.string(),
      }),
    )
    .query(async ({ input }) => {
      return db.well.findMany({
        where: { userId: input.userId },
        select: {
          id: true,
          name: true,
        },
        orderBy: {
          name: "asc",
        },
      });
    }),
  getAllRigs: protectedProcedure
    .input(
      z.object({
        userId: z.string(),
      }),
    )
    .query(async ({ input }) => {
      const dailyInfos = await db.$queryRaw<{ rig: string }[]>`
                SELECT DISTINCT "di"."rig"
                FROM "DailyInformation" AS "di"
                         JOIN "Well" AS "w" ON "di"."wellId" = "w"."id"
                WHERE "w"."userId" = ${input.userId}
                  AND "di"."rig" IS NOT NULL;
            `;

      return dailyInfos.map((info) => info.rig).filter((rig) => rig !== null);
    }),
  delete: protectedProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .mutation(async ({ input }) => {
      const { id } = input;

      return db.well.delete({
        where: { id },
      });
    }),
  findDetail: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ input }) => {
      /// TODO: verify session permission (user id)
      const well = await db.well.findUnique({
        where: { id: input.id },
        include: {
          dailyInformations: {
            orderBy: { day: "asc" },
            include: {
              drillingInformation: {
                include: {
                  mud: true,
                  parameters: { orderBy: { id: "asc" } },
                  bottomHoleAssembly: { orderBy: { id: "asc" } },
                },
              },
              activityInformation: {
                include: {
                  timelogs: { orderBy: { id: "desc" } },
                },
              },
              events: true,
            },
          },
        },
      });

      const dailyRows = well!.dailyInformations.map((d) => {
        const timelogs = d.activityInformation!.timelogs;

        let phase: string | null = null;
        for (const timelog of timelogs) {
          if (timelog.phase != null) {
            phase = timelog.phase;
            break;
          }
        }
        return {
          Day: d.day ? dayToYYYYMMDD(d.day) : "-",
          RIG: d.rig ?? "-",
          Phase: phase,
          "Accountable Party": d.accountableParty ?? "-",
          "Measure Depth": d.drillingInformation?.measureDepth
            ? (d.drillingInformation.measureDepth as unknown as number)
            : "-",
          TVD: d.drillingInformation?.totalVerticalDepth
            ? (d.drillingInformation.totalVerticalDepth as unknown as number)
            : "-",
          "Rotating Hours": d.drillingInformation?.rotatingHours
            ? (d.drillingInformation.rotatingHours as unknown as number)
            : "-",
          "Rate Penetration (ROP)": d.drillingInformation?.ratePenetration
            ? (d.drillingInformation.ratePenetration as unknown as number)
            : "-",
          "Fluid Type": d.drillingInformation?.mud?.fluidType ?? "-",
          Viscosity: d.drillingInformation?.mud?.viscosity
            ? (d.drillingInformation.mud.viscosity as unknown as number)
            : "-",
          Density: d.drillingInformation?.mud?.density
            ? (d.drillingInformation.mud.density as unknown as number)
            : "-",
          "Plastic Viscosity": d.drillingInformation?.mud?.plasticViscosity
            ? (d.drillingInformation.mud.plasticViscosity as unknown as number)
            : "-",
          "Yield Point": d.drillingInformation?.mud?.yieldPoint
            ? (d.drillingInformation.mud.yieldPoint as unknown as number)
            : "-",
          Losses: d.drillingInformation?.mud?.losses
            ? (d.drillingInformation.mud.losses as unknown as number)
            : "-",
          "Md Start": d.drillingInformation?.parameters[0]?.mdStart
            ? (d.drillingInformation.parameters[0].mdStart as unknown as number)
            : "-",
          "Md End": d.drillingInformation?.parameters[0]?.mdEnd
            ? (d.drillingInformation.parameters[0].mdEnd as unknown as number)
            : "-",
          ROP: d.drillingInformation?.parameters[0]?.rop
            ? (d.drillingInformation.parameters[0].rop as unknown as number)
            : "-",
          "Max RPM": d.drillingInformation?.parameters[0]?.maxRpm
            ? (d.drillingInformation.parameters[0].maxRpm as unknown as number)
            : "-",
          "Drilling Torque": d.drillingInformation?.parameters[0]
            ?.drillingTorque
            ? (d.drillingInformation.parameters[0]
                .drillingTorque as unknown as number)
            : "-",
          WOB: d.drillingInformation?.parameters[0]?.wob
            ? (d.drillingInformation.parameters[0].wob as unknown as number)
            : "-",
          "BHA Bit Name":
            d.drillingInformation?.bottomHoleAssembly[0]?.bhaBitName ?? "-",
          "Bit Size": d.drillingInformation?.bottomHoleAssembly[0]?.bitSize
            ? (d.drillingInformation.bottomHoleAssembly[0]
                .bitSize as unknown as number)
            : "-",
          "Bit Type":
            d.drillingInformation?.bottomHoleAssembly[0]?.bitType ?? "-",
          "Bit Serial Number":
            d.drillingInformation?.bottomHoleAssembly[0]?.bitSerialNumber ??
            "-",
          "BHA ROP": d.drillingInformation?.bottomHoleAssembly[0]?.rop
            ? (d.drillingInformation.bottomHoleAssembly[0]
                .rop as unknown as number)
            : "-",
          NPT: d.activityInformation?.npt
            ? (d.activityInformation.npt as unknown as number)
            : "-",
          Summary: d.activityInformation?.summary ?? "-",
          "Next Activity": d.activityInformation?.nextActivity ?? "-",
        };
      });

      return {
        id: well!.id,
        name: well!.name,
        type: well!.type,
        dailyRows,
      };
    }),
  getMapWells: getUAEMapWells,
  getRopVsDepthData: ropVsDepth,
  getCumulativeNPTData: cumulativeNPT,
  getTimeVsDepthData: TimeVsDepth,
});
