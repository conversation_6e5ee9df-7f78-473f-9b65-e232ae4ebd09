import { type Prisma } from "@prisma/client";
import {
  numberResolver,
  numericExtractor,
  OffsetAxis,
  ScrappingDirection,
  scrapUntil,
  nonEmptyResolver,
} from "~/utils/pdf_extractor/commons";

/**
 * Extracts bit data from the Saudi onshore PDF
 * Based on the SaudiBitData model in the Prisma schema
 */
export function saudiExtractBitData(
  table: string[][],
): Prisma.SaudiBitDataCreateWithoutDailyInformationInput {
  return {
    bitNumber: extractBitNumber(table),
    mdIn: extractMdIn(table),
    mdOut: extractMdOut(table),
    runFootage: extractRunFootage(table),
    hours: extractHours(table),
    averageROP: extractAverageROP(table),
    wob: extractWob(table),
    rpm: extractRpm(table),
    iadc: extractIadc(table),
    size: extractSize(table),
    manufacturer: extractManufacturer(table),
    pressure: extractPressure(table),
    gmp: extractGpm(table), // Field name in schema is "gmp" not "gpm"
    jetVel: extractJetVel(table),
    dpAv: extractDpAv(table),
    dcAv: extractDcAv(table),
    bitHhp: extractBitHhp(table),
    iRow: extractIRow(table),
    oRow: extractORow(table),
    dc: extractDc(table),
    location: extractLocation(table),
    bearings: extractBearings(table),
    serialNumber: extractSerialNumber(table),
    type: extractType(table),
    jets: extractJets(table),
    tfa: extractTfa(table),
    gauge: extractGauge(table),
    other: extractOther(table),
    poohReasons: extractPoohReasons(table),
  };
}

/**
 * Extracts Bit Number value from the table
 */
function extractBitNumber(table: string[][]): number | null {
  const value = scrapUntil(
    table,
    "Bit Number",
    { row: 0, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );

  return value;
}

/**
 * Extracts MD In value from the table
 */
function extractMdIn(table: string[][]): number | null {
  const value = scrapUntil(
    table,
    "MD In",
    { row: 0, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );

  return value;
}

/**
 * Extracts MD Out value from the table
 */
function extractMdOut(table: string[][]): number | null {
  const value = scrapUntil(
    table,
    "MD Out",
    { row: 0, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );

  return value;
}

/**
 * Extracts Run Footage value from the table
 */
function extractRunFootage(table: string[][]): number | null {
  const value = scrapUntil(
    table,
    "Run Footage",
    { row: 0, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );

  return value;
}

/**
 * Extracts Hours value from the table
 */
function extractHours(table: string[][]): number | null {
  const value = scrapUntil(
    table,
    "Hours",
    { row: 0, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );

  return value;
}

/**
 * Extracts Average ROP value from the table
 */
function extractAverageROP(table: string[][]): number | null {
  const value = scrapUntil(
    table,
    "Average ROP",
    { row: 0, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );

  return value;
}

/**
 * Extracts WOB value from the table
 */
function extractWob(table: string[][]): number {
  const value = scrapUntil(
    table,
    "WOB",
    { row: 0, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return 0;
      return numericExtractor(value) ?? 0;
    },
    3,
  );

  return value ?? 0;
}

/**
 * Extracts RPM value from the table
 */
function extractRpm(table: string[][]): number {
  const value = scrapUntil(
    table,
    "RPM",
    { row: 0, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return 0;
      return numericExtractor(value) ?? 0;
    },
    3,
  );

  return value ?? 0;
}

/**
 * Extracts IADC value from the table
 */
function extractIadc(table: string[][]): string {
  const value = scrapUntil(
    table,
    "IADC",
    { row: 0, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return "";
      return nonEmptyResolver(value) ?? "";
    },
    3,
  );

  return value ?? "";
}

/**
 * Extracts Size value from the table
 */
function extractSize(table: string[][]): number {
  const value = scrapUntil(
    table,
    "Size",
    { row: 0, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return 0;
      return numericExtractor(value) ?? 0;
    },
    3,
  );

  return value ?? 0;
}

/**
 * Extracts Manufacturer value from the table
 */
function extractManufacturer(table: string[][]): string {
  const value = scrapUntil(
    table,
    "Manufacturer",
    { row: 0, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return "";
      return nonEmptyResolver(value) ?? "";
    },
    3,
  );

  return value ?? "";
}

/**
 * Extracts Pressure value from the table
 */
function extractPressure(table: string[][]): number {
  const value = scrapUntil(
    table,
    "Pressure",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return 0;
      return numericExtractor(value) ?? 0;
    },
    3,
  );

  return value ?? 0;
}

/**
 * Extracts GPM value from the table
 */
function extractGpm(table: string[][]): number {
  const value = scrapUntil(
    table,
    "GPM",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return 0;
      return numericExtractor(value) ?? 0;
    },
    3,
  );

  return value ?? 0;
}

/**
 * Extracts Jet Vel value from the table
 */
function extractJetVel(table: string[][]): number {
  const value = scrapUntil(
    table,
    "Jet Vel",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return 0;
      return numericExtractor(value) ?? 0;
    },
    3,
  );

  return value ?? 0;
}

/**
 * Extracts DP Av value from the table
 */
function extractDpAv(table: string[][]): number {
  const value = scrapUntil(
    table,
    "DP Av",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return 0;
      return numericExtractor(value) ?? 0;
    },
    3,
  );

  return value ?? 0;
}

/**
 * Extracts DC Av value from the table
 */
function extractDcAv(table: string[][]): number {
  const value = scrapUntil(
    table,
    "DC Av",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return 0;
      return numericExtractor(value) ?? 0;
    },
    3,
  );

  return value ?? 0;
}

/**
 * Extracts Bit HHP value from the table
 */
function extractBitHhp(table: string[][]): number {
  const value = scrapUntil(
    table,
    "Bit HHP",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return 0;
      return numericExtractor(value) ?? 0;
    },
    3,
  );

  return value ?? 0;
}

/**
 * Extracts I Row value from the table
 */
function extractIRow(table: string[][]): number {
  const value = scrapUntil(
    table,
    "I Row",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return 0;
      return numericExtractor(value) ?? 0;
    },
    3,
  );

  return value ?? 0;
}

/**
 * Extracts O Row value from the table
 */
function extractORow(table: string[][]): number {
  const value = scrapUntil(
    table,
    "O Row",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return 0;
      return numericExtractor(value) ?? 0;
    },
    3,
  );

  return value ?? 0;
}

/**
 * Extracts DC value from the table
 */
function extractDc(table: string[][]): string {
  const value = scrapUntil(
    table,
    "DC",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return "";
      return nonEmptyResolver(value) ?? "";
    },
    3,
  );

  return value ?? "";
}

/**
 * Extracts Location value from the table
 */
function extractLocation(table: string[][]): string {
  const value = scrapUntil(
    table,
    "Location",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return "";
      return nonEmptyResolver(value) ?? "";
    },
    3,
  );

  return value ?? "";
}

/**
 * Extracts Bearings value from the table
 */
function extractBearings(table: string[][]): string {
  const value = scrapUntil(
    table,
    "Bearings",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return "";
      return nonEmptyResolver(value) ?? "";
    },
    3,
  );

  return value ?? "";
}

/**
 * Extracts Serial Number value from the table
 */
function extractSerialNumber(table: string[][]): string {
  const value = scrapUntil(
    table,
    "Serial Number",
    { row: 2, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return "";
      return nonEmptyResolver(value) ?? "";
    },
    3,
  );

  return value ?? "";
}

/**
 * Extracts Type value from the table
 */
function extractType(table: string[][]): string {
  const value = scrapUntil(
    table,
    "Type",
    { row: 2, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return "";
      return nonEmptyResolver(value) ?? "";
    },
    3,
  );

  return value ?? "";
}

/**
 * Extracts Jets value from the table
 */
function extractJets(table: string[][]): string {
  const value = scrapUntil(
    table,
    "Jets",
    { row: 2, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return "";
      return nonEmptyResolver(value) ?? "";
    },
    3,
  );

  return value ?? "";
}

/**
 * Extracts TFA value from the table
 */
function extractTfa(table: string[][]): number {
  const value = scrapUntil(
    table,
    "TFA",
    { row: 2, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return 0;
      return numericExtractor(value) ?? 0;
    },
    3,
  );

  return value ?? 0;
}

/**
 * Extracts Gauge value from the table
 */
function extractGauge(table: string[][]): number {
  const value = scrapUntil(
    table,
    "Gauge",
    { row: 3, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return 0;
      return numericExtractor(value) ?? 0;
    },
    3,
  );

  return value ?? 0;
}

/**
 * Extracts Other value from the table
 */
function extractOther(table: string[][]): string {
  const value = scrapUntil(
    table,
    "Other",
    { row: 3, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return "";
      return nonEmptyResolver(value) ?? "";
    },
    3,
  );

  return value ?? "";
}

/**
 * Extracts POOH Reasons value from the table
 */
function extractPoohReasons(table: string[][]): string {
  return ""; // This field is not visible in the provided image, so returning empty string as default
}
