import { type Prisma } from "@prisma/client";
import {
  numberResolver,
  numericExtractor,
  OffsetAxis,
  ScrappingDirection,
  scrapUntil,
  nonEmptyResolver,
} from "~/utils/pdf_extractor/commons";

/**
 * Extracts bit data from the Saudi onshore PDF
 * Based on the SaudiBitData model in the Prisma schema
 */
export function saudiExtractBitData(
  table: string[][],
): Prisma.SaudiBitDataCreateWithoutDailyInformationInput {
  return {
    bitNumber: extractBitNumber(table),
    mdIn: extractMdIn(table),
    mdOut: extractMdOut(table),
    runFootage: extractRunFootage(table),
    hours: extractHours(table),
    averageROP: extractAverageROP(table),
    wob: extractWob(table),
    rpm: extractRpm(table),
    iadc: extractIadc(table),
    size: extractSize(table),
    manufacturer: extractManufacturer(table),
    pressure: extractPressure(table),
    gmp: extractGpm(table), // Field name in schema is "gmp" not "gpm"
    jetVel: extractJetVel(table),
    dpAv: extractDpAv(table),
    dcAv: extractDcAv(table),
    bitHhp: extractBitHhp(table),
    iRow: extractIRow(table),
    oRow: extractORow(table),
    dc: extractDc(table),
    location: extractLocation(table),
    bearings: extractBearings(table),
    serialNumber: extractSerialNumber(table),
    type: extractType(table),
    jets: extractJets(table),
    tfa: extractTfa(table),
    gauge: extractGauge(table),
    other: extractOther(table),
    poohReasons: extractPoohReasons(table),
  };
}

function extractBitNumber(table: string[][]): number | null {
  return scrapUntil(
    table,
    "Bit Number",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );
}

function extractMdIn(table: string[][]): number | null {
  return scrapUntil(
    table,
    "MD In",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.decreasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );
}

function extractMdOut(table: string[][]): number | null {
  return scrapUntil(
    table,
    "MD Out",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.decreasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );
}

function extractRunFootage(table: string[][]): number | null {
  return scrapUntil(
    table,
    "Run Footage",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );
}

function extractHours(table: string[][]): number | null {
  return scrapUntil(
    table,
    "Hours",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );
}

function extractAverageROP(table: string[][]): number | null {
  return scrapUntil(
    table,
    "Average ROP",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );
}

function extractWob(table: string[][]): number | null {
  return scrapUntil(
    table,
    "WOB",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );
}

function extractRpm(table: string[][]): number | null {
  return scrapUntil(
    table,
    "RPM",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );
}

function extractIadc(table: string[][]): string | null {
  return scrapUntil(
    table,
    "IADC",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.decreasing,
    (value) => {
      if (value === "N/A") return null;
      return nonEmptyResolver(value);
    },
    3,
  );
}

function extractSize(table: string[][]): number | null {
  return scrapUntil(
    table,
    "Size",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );
}

function extractManufacturer(table: string[][]): string | null {
  return scrapUntil(
    table,
    "Manufacturer",
    { row: 0, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return nonEmptyResolver(value);
    },
    3,
  );
}

function extractPressure(table: string[][]): number | null {
  return scrapUntil(
    table,
    "Pressure",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );
}

function extractGpm(table: string[][]): number | null {
  const value = scrapUntil(
    table,
    "GPM",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );

  return value;
}

function extractJetVel(table: string[][]): number | null {
  return scrapUntil(
    table,
    "Jet Vel",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );
}

function extractDpAv(table: string[][]): number | null {
  const value = scrapUntil(
    table,
    "DP Av",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );

  return value;
}

function extractDcAv(table: string[][]): number | null {
  return scrapUntil(
    table,
    "DC Av",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );
}

function extractBitHhp(table: string[][]): number | null {
  return scrapUntil(
    table,
    "Bit HHP",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );
}

function extractIRow(table: string[][]): number | null {
  return scrapUntil(
    table,
    "I-Row",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );
}

function extractORow(table: string[][]): number | null {
  return scrapUntil(
    table,
    "O-Row",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );
}

function extractDc(table: string[][]): string | null {
  return scrapUntil(
    table,
    "DC",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.decreasing,
    (value) => {
      if (value === "N/A") return null;
      return nonEmptyResolver(value);
    },
    3,
  );
}

function extractLocation(table: string[][]): string | null {
  return scrapUntil(
    table,
    "Location",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return nonEmptyResolver(value);
    },
    3,
  );
}


function extractBearings(table: string[][]): string | null {
  return scrapUntil(
    table,
    "Bearings",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return nonEmptyResolver(value);
    },
    3,
  );
}

function extractSerialNumber(table: string[][]): string | null {
  return scrapUntil(
    table,
    "Serial Number",
    { row: 2, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return nonEmptyResolver(value);
    },
    3,
  );
}

/**
 * Extracts Type value from the table
 */
function extractType(table: string[][]): string | null {
  return scrapUntil(
    table,
    "Type",
    { row: 2, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return nonEmptyResolver(value);
    },
    3,
  );
}

/**
 * Extracts Jets value from the table
 */
function extractJets(table: string[][]): string | null {
  return scrapUntil(
    table,
    "Jets",
    { row: 2, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return nonEmptyResolver(value);
    },
    3,
  );
}

/**
 * Extracts TFA value from the table
 */
function extractTfa(table: string[][]): number | null {
  return scrapUntil(
    table,
    "TFA",
    { row: 2, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );
}

/**
 * Extracts Gauge value from the table
 */
function extractGauge(table: string[][]): number | null {
  return scrapUntil(
    table,
    "Gauge",
    { row: 3, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );
}

/**
 * Extracts Other value from the table
 */
function extractOther(table: string[][]): string | null {
  return scrapUntil(
    table,
    "Other",
    { row: 3, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return nonEmptyResolver(value);
    },
    3,
  );
}

/**
 * Extracts POOH Reasons value from the table
 */
function extractPoohReasons(table: string[][]): string | null {
  return null; // This field is not visible in the provided image, so returning null as default
}
