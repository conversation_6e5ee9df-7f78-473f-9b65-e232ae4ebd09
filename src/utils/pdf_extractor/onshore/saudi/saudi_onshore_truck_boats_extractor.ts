import { type Prisma } from "@prisma/client";
import {
  OffsetAxis,
  ScrappingDirection,
  scrapUntil,
  nonEmptyResolver,
} from "~/utils/pdf_extractor/commons";

/**
 * Extracts truck/boats data from the Saudi onshore PDF
 * Based on the SaudiTruckBoats model in the Prisma schema
 */
export function saudiExtractTruckBoats(
  table: string[][],
): Prisma.SaudiTruckBoatsCreateWithoutDailyInformationInput {
  return {
    standbyTankers: extractStandbyTankers(table),
  };
}

/**
 * Extracts Standby Tankers value from the table
 */
function extractStandbyTankers(table: string[][]): string | null {
  return scrapUntil(
    table,
    "Standby/Tankers",
    { row: 0, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return nonEmptyResolver(value);
    },
    3,
  );
}
