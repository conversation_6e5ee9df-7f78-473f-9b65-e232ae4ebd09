import { type Prisma } from "@prisma/client";
import {
  numericExtractor,
  OffsetAxis,
  ScrappingDirection,
  scrapUntil,
  nonEmptyResolver,
} from "~/utils/pdf_extractor/commons";

export function saudiExtractDrillString(
  table: string[][],
): Prisma.SaudiDrillStringCreateWithoutDailyInformationInput {
  return {
    float: extractFloat(table),
    bhaHours: extractBhaHours(table),
    stringWt: extractStringWt(table),
    pickUp: extractPickUp(table),
    slackOff: extractSlackOff(table),
    rotTorque: extractRotTorque(table),
    jasrSerial: extractJasrSerial(table),
    jarsHours: extractJarsHours(table),
    shockSubSerial: extractShockSubSerial(table),
    shockSubHours: extractShockSubHours(table),
    mudMotorSerial: extractMudMotorSerial(table),
    mudMotorHours: extractMudMotorHours(table),
  };
}

function extractFloat(table: string[][]): string | null {
  return scrapUntil(
    table,
    "Float",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return nonEmptyResolver(value);
    },
    3,
  );
}

function extractBhaHours(table: string[][]): number | null {
  return scrapUntil(
    table,
    "BHA Hrs",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );
}

function extractStringWt(table: string[][]): number | null {
  return scrapUntil(
    table,
    "String Wt.",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );
}

function extractPickUp(table: string[][]): number | null {
  return scrapUntil(
    table,
    "Pick Up",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );
}

function extractSlackOff(table: string[][]): number | null {
  return scrapUntil(
    table,
    "Slack Off",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );
}

function extractRotTorque(table: string[][]): number | null {
  return scrapUntil(
    table,
    "Rot. Torque",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    5,
  );
}

function extractJasrSerial(table: string[][]): number | null {
  return scrapUntil(
    table,
    "Jars Serial #",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );
}

function extractJarsHours(table: string[][]): number | null {
  return scrapUntil(
    table,
    "Jars Hrs",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );
}

function extractShockSubSerial(table: string[][]): string | null {
  return scrapUntil(
    table,
    "Shock Sub Serial #",
    { row: 0, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return nonEmptyResolver(value);
    },
    3,
  );
}

function extractShockSubHours(table: string[][]): number | null {
  return scrapUntil(
    table,
    "Shock Sub Hrs",
    { row: 0, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );
}

function extractMudMotorSerial(table: string[][]): string | null {
  return scrapUntil(
    table,
    "Mud Motor Serial #",
    { row: 0, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return nonEmptyResolver(value);
    },
    3,
  );
}

function extractMudMotorHours(table: string[][]): number | null {
  return scrapUntil(
    table,
    "Mud Motor Hrs",
    { row: 0, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );
}
