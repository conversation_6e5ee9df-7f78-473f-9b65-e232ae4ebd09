import { type Prisma } from "@prisma/client";
import {
  numericExtractor,
  OffsetAxis,
  ScrappingDirection,
  scrapUntil,
} from "~/utils/pdf_extractor/commons";

export function saudiExtractBulk(
  table: string[][],
): Prisma.SaudiBulkCreateWithoutDailyInformationInput {
  return {
    drillWtrBbls: extractDrillWtrBbls(table),
    potWtrBbls: extractPotWtrBbls(table),
    fuelBbls: extractFuelBbls(table),
    bariteSx: extractBariteSx(table),
    bentonine: extractBentonine(table),
    cementGSx: extractCementGSx(table),
  };
}

function extractDrillWtrBbls(table: string[][]): number | null {
  return scrapUntil(
    table,
    "Drill Wtr-BBLS",
    { row: 2, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );
}

function extractPotWtrBbls(table: string[][]): number | null {
  return scrapUntil(
    table,
    "Pot Wtr.-BBLS",
    { row: 2, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value) ?? null;
    },
    5,
  );
}

function extractFuelBbls(table: string[][]): number | null {
  return scrapUntil(
    table,
    "Fuel- BBLS",
    { row: 2, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );
}

function extractBariteSx(table: string[][]): number | null {
  return scrapUntil(
    table,
    "Barite- SX",
    { row: 2, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value) ?? null;
    },
    3,
  );
}

function extractBentonine(table: string[][]): number | null {
  return scrapUntil(
    table,
    "Bentonite-SX",
    { row: 2, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value) ?? null;
    },
    3,
  );
}

function extractCementGSx(table: string[][]): number | null {
  return scrapUntil(
    table,
    "Cement G-SX",
    { row: 2, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value) ?? null;
    },
    3,
  );
}
