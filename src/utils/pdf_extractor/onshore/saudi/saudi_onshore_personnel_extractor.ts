import { type Prisma } from "@prisma/client";
import {
  numericExtractor,
  OffsetAxis,
  ScrappingDirection,
  scrapUntil,
  nonEmptyResolver,
} from "~/utils/pdf_extractor/commons";

/**
 * Extracts personnel data from the Saudi onshore PDF
 * Based on the SaudiPersonnel model in the Prisma schema
 * Returns an array of personnel records since this is a table with multiple rows
 */
export function saudiExtractPersonnel(
  table: string[][],
): Prisma.SaudiPersonnelCreateWithoutDailyInformationInput[] {
  const personnelData: Prisma.SaudiPersonnelCreateWithoutDailyInformationInput[] = [];

  // Define the companies and categories based on the image
  const companies = ["ITS", "GOS", "FET", "BHI", "RIG", "OES", "SCD", "RIG", "ARM", "SCA", "ARM", "CAM"];
  const categories = ["CSR", "CTRG", "DV", "DPDS", "RIG", "DPMS", "LWD", "OTHR", "OTHR", "PCS", "WSC", "WHCS"];

  // Extract data for each company/category combination
  for (let i = 0; i < companies.length; i++) {
    const company = companies[i];
    const category = categories[i];

    const numberOfPersons = extractDataByColumnIndex(table, "No. of Persons", i + 1);
    const onLocHours = extractDataByColumnIndex(table, "Personnel on Loc. Hrs", i + 1);
    const operatingHours = extractDataByColumnIndex(table, "Operating Hours", i + 1);

    // Only add record if at least one field has data
    if (numberOfPersons !== null || onLocHours !== null || operatingHours !== null) {
      personnelData.push({
        company,
        category,
        numberOfPersons,
        onLocHours,
        operatingHours,
      });
    }
  }

  return personnelData;
}

/**
 * Extracts data from a specific row and column index
 */
function extractDataByColumnIndex(
  table: string[][],
  rowLabel: string,
  columnIndex: number,
): number | null {
  // Find the row with the specified label
  for (let row = 0; row < table.length; row++) {
    for (let col = 0; col < table[row].length; col++) {
      if (table[row][col] === rowLabel) {
        // Found the row, now get the value from the specified column
        if (table[row][columnIndex]) {
          const value = table[row][columnIndex];
          if (value === "N/A" || value === "" || value === "0") return null;
          return numericExtractor(value);
        }
        return null;
      }
    }
  }
  return null;
}
