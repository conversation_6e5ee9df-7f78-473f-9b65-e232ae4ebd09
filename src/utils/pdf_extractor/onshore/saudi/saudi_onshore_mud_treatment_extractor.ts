import { type Prisma } from "@prisma/client";
import {
  numberResolver,
  numericExtractor,
  OffsetAxis,
  ScrappingDirection,
  scrapUntil,
} from "~/utils/pdf_extractor/commons";

/**
 * Extracts mud treatment data from the Saudi onshore PDF
 * Based on the SaudiMudTreatment model in the Prisma schema
 */
export function saudiExtractMudTreatment(
  table: string[][],
): Prisma.SaudiMudTreatmentCreateWithoutDailyInformationInput {
  return {
    barite: extractBarite(table),
    bent: extractBent(table),
    nacl: extractNacl(table),
    caust: extractCaust(table),
    sodash: extractSodash(table),
    bctrn: extractBctrn(table),
    polySal: extractPolySal(table),
    hs2ScavL: extractHs2ScavL(table),
    xc: extractXc(table),
    micaFine: extractMicaFine(table),
    baroFibrs: extractBaroFibrs(table),
    bFibreSf: extractBFibreSf(table),
    micaCrse: extractMicaCrse(table),
    mrlbM: extractMrlbM(table),
    mrlbC: extractMrlbC(table),
    mrlbF: extractMrlbF(table),
  };
}

/**
 * Extracts OXYGEN value from the table
 */
function extractBarite(table: string[][]): number | null {
  return scrapUntil(
    table,
    "OXYGEN",
    { row: 0, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );
}

/**
 * Extracts H2S SCAV-L value from the table
 */
function extractHs2ScavL(table: string[][]): number | null {
  return scrapUntil(
    table,
    "H2S SCAV-L",
    { row: 0, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );
}

/**
 * Extracts NEWDRILL PLUS value from the table
 */
function extractBent(table: string[][]): number | null {
  return scrapUntil(
    table,
    "NEWDRILL PLUS",
    { row: 0, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );
}

/**
 * Extracts PAC-LV value from the table
 */
function extractNacl(table: string[][]): number | null {
  return scrapUntil(
    table,
    "PAC-LV",
    { row: 0, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );
}

/**
 * Extracts XC value from the table
 */
function extractXc(table: string[][]): number | null {
  return scrapUntil(
    table,
    "XC",
    { row: 0, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );
}

/**
 * Extracts BCTRN value from the table
 */
function extractBctrn(table: string[][]): number | null {
  return scrapUntil(
    table,
    "BCTRN",
    { row: 0, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );
}

/**
 * Extracts CAUST value from the table
 */
function extractCaust(table: string[][]): number | null {
  return scrapUntil(
    table,
    "CAUST",
    { row: 0, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );
}

/**
 * Extracts MAGSHIELD value from the table (for micaFine)
 */
function extractMicaFine(table: string[][]): number | null {
  return scrapUntil(
    table,
    "MAGSHIELD",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );
}

/**
 * Extracts DRILTREX value from the table (for baroFibrs)
 */
function extractBaroFibrs(table: string[][]): number | null {
  return scrapUntil(
    table,
    "DRILTREX",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );
}

/**
 * Extracts PACE value from the table (for bFibreSf)
 */
function extractBFibreSf(table: string[][]): number | null {
  return scrapUntil(
    table,
    "PACE",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );
}

/**
 * Extracts CITRIC ACID value from the table (for micaCrse)
 */
function extractMicaCrse(table: string[][]): number | null {
  return scrapUntil(
    table,
    "CITRIC ACID",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );
}

/**
 * Extracts SODA ASH value from the table (for sodash)
 */
function extractSodash(table: string[][]): number | null {
  return scrapUntil(
    table,
    "SODA ASH",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );
}

/**
 * Extracts STABIL value from the table (for polySal)
 */
function extractPolySal(table: string[][]): number | null {
  return scrapUntil(
    table,
    "STABIL",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );
}

/**
 * Extracts VERSASWEP value from the table (for mrlbM)
 */
function extractMrlbM(table: string[][]): number | null {
  return scrapUntil(
    table,
    "VERSASWEP",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );
}

/**
 * Extracts WOBLM value from the table (for mrlbC)
 */
function extractMrlbC(table: string[][]): number | null {
  return scrapUntil(
    table,
    "WOBLM",
    { row: 2, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );
}

/**
 * Extracts last value from the table (for mrlbF)
 * This is a placeholder since we don't have a specific label for mrlbF in the image
 */
function extractMrlbF(table: string[][]): number | null {
  return null; // No specific label for mrlbF in the image
}
