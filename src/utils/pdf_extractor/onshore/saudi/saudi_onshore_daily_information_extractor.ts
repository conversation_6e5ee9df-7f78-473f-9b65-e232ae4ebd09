import { type Prisma } from "@prisma/client";
import { dayFromString } from "~/utils/date/date_utils";
import {
  OffsetAxis,
  ScrappingDirection,
  scrapUntil,
} from "~/utils/pdf_extractor/commons";
import { saudiExtractDailyMud } from "~/utils/pdf_extractor/onshore/saudi/saudi_onshore_mud_data_extractor";
import { saudiExtractMiscellaneous } from "~/utils/pdf_extractor/onshore/saudi/saudi_onshore_miscellaneous_extractor";
import { saudiExtractBulk } from "~/utils/pdf_extractor/onshore/saudi/saudi_onshore_bulk_extractor";
import { saudiExtractBitData } from "~/utils/pdf_extractor/onshore/saudi/saudi_onshore_bit_data_extractor";
import { saudiExtractDrillString } from "~/utils/pdf_extractor/onshore/saudi/saudi_onshore_drill_string_extractor";

export function saudiExtractDailyInformation(
  table: string[][],
  ddrId: string,
  ddrPage: number,
): Prisma.SaudiDailyInformationCreateWithoutWellInput {
  return {
    day: extractDay(table),
    rig: extractRig(table),
    ddrPage: ddrPage,
    mudData: {
      create: saudiExtractDailyMud(table),
    },
    miscellaneus: {
      create: saudiExtractMiscellaneous(table),
    },
    bulk: {
      create: saudiExtractBulk(table),
    },
    bitData: {
      create: saudiExtractBitData(table),
    },
    drillString: {
      create: saudiExtractDrillString(table),
    },
    ddr: {
      connect: {
        id: ddrId,
      },
    },
  };
}

function extractDay(table: string[][]): number | null {
  return scrapUntil(
    table,
    "Date:",
    { row: 0, column: 1 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value == undefined || value.length == 0) {
        console.error(
          "onshore-daily-information-extractor: Date not found in this DDR",
        );
        return null;
      }

      const dateMatch = RegExp(/\d{2}\/\d{2}\/\d{4}/).exec(value.trim());
      if (dateMatch) {
        return dayFromString(dateMatch[0]);
      } else {
        return;
      }
    },
  );
}

function extractRig(table: string[][]): string | null {
  return scrapUntil(
    table,
    "Rig",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.decreasing,
    (value: string) => {
      if (value == undefined || value.length == 0) {
        console.error(
          "onshore-daily-information-extractor: Rig not found in this DDR",
        );
        return null;
      }

      return value.trim();
    },
  );
}
