import { type Prisma } from "@prisma/client";
import {
  OffsetAxis,
  ScrappingDirection,
  scrapUntil,
} from "~/utils/pdf_extractor/commons";

export function saudiOnshoreProjectDataExtractor(
  table: string[][],
): Prisma.SaudiProjectDataCreateWithoutDailyInformationInput {
  return {
    charge: extractCharge(table),
    wellbores: extractWellbores(table),
    thuraya: extractThuraya(table),
    rigFormatVsat: extractRigFormatVsat(table),
    last24HourOperation: extractLast24HourOperation(table),
    next24HourPlan: extractNext24HourPlan(table),
    location: extractLocation(table),
    nextLocation: extractNextLocation(table),
  };
}

function extractCharge(table: string[][]): string | null {
  return scrapUntil(
    table,
    "Charge #:",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => value,
    3,
  );
}

function extractWellbores(table: string[][]): string | null {
  return scrapUntil(
    table,
    "Wellbores:",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => value,
    3,
  );
}

function extractThuraya(table: string[][]): string | null {
  return scrapUntil(
    table,
    "THURAYA",
    { row: 2, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => value,
    3,
  );
}

function extractLast24HourOperation(table: string[][]): string | null {
  return scrapUntil(
    table,
    "Last 24 hr operations",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => value,
    3,
  );
}

function extractRigFormatVsat(table: string[][]): string | null {
  return scrapUntil(
    table,
    "RIG FORMAN VSAT",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => value,
    3,
  );
}

function extractNext24HourPlan(table: string[][]): string | null {
  return scrapUntil(
    table,
    "RIG FORMAN VSAT",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => value,
    3,
  );
}

function extractLocation(table: string[][]): string | null {
  return scrapUntil(
    table,
    "Location",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => value,
    3,
  );
}

function extractNextLocation(table: string[][]): string | null {
  return scrapUntil(
    table,
    "Next Location",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => value,
    3,
  );
}
