import { db } from "~/server/db";
import { saudiExtractOnShoreWell } from "~/utils/pdf_extractor/onshore/saudi/saudi_onshore_well_extractor";
import { saudiExtractDailyInformation } from "~/utils/pdf_extractor/onshore/saudi/saudi_onshore_daily_information_extractor";

const kReportHeader = "D&WO Morning Report (Service Providers View)";

export async function onSaudiOnshoreExtractor(
  table: string[][],
  userId: string,
  fileName: string,
): Promise<void> {
  let initCurrentPageIndex = 0;

  const ddr = await db.ddr.create({
    data: {
      format: 0,
      fileName,
    },
  });

  let page = 1;
  await db.$transaction(
    async (tx) => {
      for (let i = 8; i < table.length; i++) {
        if (table[i]!.includes(kReportHeader) || i == table.length - 1) {
          console.info(
            `scrapping page[${page}]: ${initCurrentPageIndex}->${i}`,
          );

          const tablePage = table.slice(initCurrentPageIndex, i);
          const newWell = saudiExtractOnShoreWell(tablePage, userId);

          const existingWell = await tx.well.findFirst({
            where: { name: newWell.name, userId: userId },
            select: { id: true },
          });

          const wellDailyInformation = saudiExtractDailyInformation(
            tablePage,
            ddr.id,
            page,
          );
          if (existingWell == null) {
            console.info(`on-shore-extractor: new well`);
            await tx.well.create({
              data: {
                ...newWell,
                saudiDailyInformations: { create: [wellDailyInformation] },
              },
            });
          } else {
            console.info(
              `saudi-on-shore-extractor: existing well, adding daily information`,
            );

            const existingDailyInformation =
              await tx.saudiDailyInformation.findFirst({
                where: {
                  wellId: existingWell.id,
                  day: wellDailyInformation.day,
                },
                select: { id: true },
              });

            if (existingDailyInformation != null) {
              console.info(
                `on-shore-extractor: day already registered [${existingDailyInformation.id}]`,
              );
            } else {
              console.info(
                `on-shore-extractor: new daily information to well [${existingWell.id}]`,
              );
              await tx.saudiDailyInformation.create({
                data: {
                  well: { connect: { id: existingWell.id } },
                  ...wellDailyInformation,
                },
              });
            }
          }

          initCurrentPageIndex = i;
        }
      }
      page++;
    },
    { timeout: 3000000 },
  );
}
