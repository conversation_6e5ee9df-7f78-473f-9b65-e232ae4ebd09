import { type Prisma } from "@prisma/client";
import {
  numberResolver,
  numericExtractor,
  OffsetAxis,
  ScrappingDirection,
  scrapUntil,
  nonEmptyResolver,
} from "~/utils/pdf_extractor/commons";

export function saudiExtractDailyMud(
  table: string[][],
): Prisma.SaudiMudDataCreateWithoutDailyInformationInput {
  const [gelsSec, gelsMin] = extractGels(table);
  const [rpm3, rpm6] = extractRpm(table);
  return {
    lgs: extractLgs(table),
    cakeHthp: extractCakeHthp(table),
    cakeApi: extractCakeApi(table),
    cappm: extractCappm(table),
    clppm: extractClppm(table),
    elecStability: extractElecStability(table),
    filtrateApi: extractFiltrateApi(table),
    filtrateHthp: extractFiltrateHthp(table),
    flTemp: extractFlTemp(table),
    funnel: extractFunnel(table),
    gelsMin: gelsMin,
    gelsSec: gelsSec,
    mbt: extractMbt(table),
    mudType: extractMudType(table),
    oilVol: extractOilVol(table),
    ph: extractPh(table),
    pptTotal: extractPptTotal(table),
    pptsSpurt: extractPptsSpurt(table),
    pv: extractPv(table),
    rpm3: rpm3,
    rpm6: rpm6,
    sandVol: extractSandVol(table),
    solidsVol: extractSolidsVol(table),
    waterVol: extractWaterVol(table),
    weight: extractWeight(table),
    yp: extractYp(table),
  };
}

function extractLgs(table: string[][]): number | null {
  return scrapUntil(
    table,
    "% LGS",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    numberResolver,
    5,
  );
}

function extractCakeApi(table: string[][]): number | null {
  return scrapUntil(
    table,
    "Cake API",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    numberResolver,
    5,
  );
}

function extractCakeHthp(table: string[][]): number | null {
  return scrapUntil(
    table,
    "Cake HTHP",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    numberResolver,
    5,
  );
}

function extractCappm(table: string[][]): number | null {
  return scrapUntil(
    table,
    "CA PPM",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    numberResolver,
    3,
  );
}

function extractClppm(table: string[][]): number | null {
  return scrapUntil(
    table,
    "CL PPM",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    numberResolver,
    3,
  );
}

function extractElecStability(table: string[][]): number | null {
  return scrapUntil(
    table,
    "Elec. Stability",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    numericExtractor,
    3,
  );
}

function extractFiltrateApi(table: string[][]): number | null {
  return scrapUntil(
    table,
    "Filtrate(WL)",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    numericExtractor,
    3,
  );
}

function extractFiltrateHthp(table: string[][]): number | null {
  return scrapUntil(
    table,
    "Filtrate(WL)",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    numericExtractor,
    3,
    1,
  );
}

function extractFlTemp(table: string[][]): number | null {
  return scrapUntil(
    table,
    "FL Temp",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    numberResolver,
    3,
  );
}

function extractFunnel(table: string[][]): number | null {
  return scrapUntil(
    table,
    "Vis.(SEC)",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    numberResolver,
    3,
  );
}

function extractGels(table: string[][]): [number | null, number | null] {
  return scrapUntil(
    table,
    "Gels Sec/Min",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value == undefined || value.length == 0) {
        return [null, null];
      }

      const parts = value.split("/");

      return [parseFloat(parts[0]!), parseFloat(parts[1]!)];
    },
    3,
  )!;
}

function extractMbt(table: string[][]): number | null {
  return scrapUntil(
    table,
    "MBT",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    numberResolver,
    3,
  );
}

function extractMudType(table: string[][]): string | null {
  return scrapUntil(
    table,
    "Mud Type",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    nonEmptyResolver,
    3,
  );
}

function extractPh(table: string[][]): number | null {
  return scrapUntil(
    table,
    "PH",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    numericExtractor,
    3,
  );
}

function extractPv(table: string[][]): number | null {
  return scrapUntil(
    table,
    "PV",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    numericExtractor,
    3,
  );
}

function extractYp(table: string[][]): number | null {
  return scrapUntil(
    table,
    "YP",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    numericExtractor,
    3,
  );
}

function extractRpm(table: string[][]): [number | null, number | null] {
  return scrapUntil(
    table,
    "3/6 RPM",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value == undefined || value.length == 0) {
        return [null, null];
      }

      const parts = value.split("/");

      return [parseFloat(parts[0]!), parseFloat(parts[1]!)];
    },
    3,
  )!;
}

function extractWeight(table: string[][]): number | null {
  return scrapUntil(
    table,
    "Weight",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    numericExtractor,
    3,
  );
}

function extractOilVol(table: string[][]): number | null {
  return scrapUntil(
    table,
    "Oil Vol. %",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    numericExtractor,
    3,
  );
}

function extractWaterVol(table: string[][]): number | null {
  return scrapUntil(
    table,
    "Water Vol. %",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    numericExtractor,
    3,
  );
}

function extractSolidsVol(table: string[][]): number | null {
  return scrapUntil(
    table,
    "Solids Vol. %",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    numericExtractor,
    3,
  );
}

function extractSandVol(table: string[][]): number | null {
  return scrapUntil(
    table,
    "Sand Vol. %",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    numericExtractor,
    3,
  );
}

function extractPptTotal(table: string[][]): number | null {
  return scrapUntil(
    table,
    "PPT Total",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    numberResolver,
    2,
  );
}

function extractPptsSpurt(table: string[][]): number | null {
  return scrapUntil(
    table,
    "PPT Spurt",
    { row: 1, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    numberResolver,
    2,
  );
}
