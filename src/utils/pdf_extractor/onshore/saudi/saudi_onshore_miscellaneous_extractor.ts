import { type Prisma } from "@prisma/client";
import {
  numberResolver,
  numericExtractor,
  OffsetAxis,
  ScrappingDirection,
  scrapUntil,
  nonEmptyResolver,
} from "~/utils/pdf_extractor/commons";

export function saudiExtractMiscellaneous(
  table: string[][],
): Prisma.SaudiMiscellaneusCreateWithoutDailyInformationInput {
  return {
    bopTest: extractBopTest(table),
    bopDrills: extractBopDrills(table),
    wind: extractWind(table),
    sea: extractSea(table),
    wheater: extractWeather(table), // Field name in schema is "wheater" not "weather"
    dslta: extractDelta(table),
    safetyMeeting: extractSafetyMeeting(table),
  };
}

function extractBopTest(table: string[][]): number | null {
  return scrapUntil(
    table,
    "BOP Test",
    { row: 0, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value);
    },
    3,
  );
}

function extractBopDrills(table: string[][]): number | null {
  return scrapUntil(
    table,
    "BOP Drills",
    { row: 0, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numericExtractor(value) ?? 0;
    },
    3,
  );
}

function extractWind(table: string[][]): string | null {
  return scrapUntil(
    table,
    "Wind",
    { row: 0, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return nonEmptyResolver(value) ?? null;
    },
    3,
  );
}

function extractSea(table: string[][]): string | null {
  return scrapUntil(
    table,
    "Sea",
    { row: 0, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return nonEmptyResolver(value) ?? null;
    },
    3,
  );
}

function extractWeather(table: string[][]): string | null {
  return scrapUntil(
    table,
    "Weather",
    { row: 0, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return nonEmptyResolver(value) ?? null;
    },
    3,
  );
}

function extractDelta(table: string[][]): number | null {
  return scrapUntil(
    table,
    "DSLTA",
    { row: 0, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.increasing,
    (value) => {
      if (value === "N/A") return null;
      return numberResolver(value) ?? null;
    },
    3,
  );
}

function extractSafetyMeeting(table: string[][]): string | null {
  return scrapUntil(
    table,
    "Safety Meeting",
    { row: 0, column: 0 },
    OffsetAxis.horizontal,
    ScrappingDirection.decreasing,
    (value) => {
      if (value === "N/A") return null;
      return nonEmptyResolver(value) ?? null;
    },
    3,
  );
}
