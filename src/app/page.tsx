"use client";
import React, { useEffect, useState } from "react";
import { api } from "~/trpc/react";
import AppHeader from "src/shared/components/AppHeader";
import "./App.css";
import WellsSelector from "~/app/_components/wells_selector/WellsSelector";
import WellsDataView from "~/app/_components/wells_data_view/WellsDataView";
import {
  Dialog,
  DialogBackdrop,
  DialogPanel,
  DialogTitle,
} from "@headlessui/react";
import { signOut, useSession } from "next-auth/react";
import Link from "next/link";

export default function Home() {
  const [open, setOpen] = useState(false);
  const { data: session, status } = useSession();
  const { data: extraData } = api.newRouter.ruta.useQuery();

  const { data: isAgreed, isLoading } = api.user.getIsAgreeTermsOfUser.useQuery(
    {
      userId: session?.user.userId!,
    },
    {
      enabled: status == "authenticated",
    },
  );
  const agreeMutation = api.user.setAgree.useMutation();

  useEffect(() => {
    if (!(isAgreed ?? true) && !isLoading) {
      setOpen(true);
    }
  }, [isAgreed, isLoading]);

  async function onAgree() {
    agreeMutation.mutate({ userId: session?.user.userId! });
    setOpen(false);
  }

  return (
    <>
      <h1>{extraData?.msg ?? "loading..."}</h1>
      <AppHeader current={"main"} />
      <div className="main-container">
        <main className="grid grid-cols-2">
          <div className="sticky top-0 col-span-1 h-screen border-r-2">
            <WellsSelector />
          </div>
          <div className="col-start-2 h-full">
            <WellsDataView />
          </div>
        </main>
      </div>
      <Dialog open={open} onClose={setOpen} className="relative z-10">
        <DialogBackdrop
          transition
          className="fixed inset-0 bg-gray-500/75 transition-opacity data-closed:opacity-0 data-enter:duration-300 data-enter:ease-out data-leave:duration-200 data-leave:ease-in"
        />

        <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <DialogPanel
              transition
              className="relative transform overflow-hidden rounded-lg bg-white px-4 pt-5 pb-4 text-left shadow-xl transition-all data-closed:translate-y-4 data-closed:opacity-0 data-enter:duration-300 data-enter:ease-out data-leave:duration-200 data-leave:ease-in sm:my-8 sm:w-full sm:max-w-lg sm:p-6 sm:data-closed:translate-y-0 sm:data-closed:scale-95"
            >
              <div>
                <div className="mt-3 text-center sm:mt-5">
                  <DialogTitle
                    as="h3"
                    className="text-base font-semibold text-gray-900"
                  >
                    Terms and Conditions
                  </DialogTitle>
                  <div className="mt-2">
                    <p className="text-sm text-gray-500">
                      Before using Drillvisor, you must accept our{" "}
                      <Link href="#" className="font-bold text-blue-500">
                        terms and conditions.
                      </Link>
                    </p>
                  </div>
                </div>
              </div>
              <div className="mt-5 sm:mt-6 sm:grid sm:grid-flow-row-dense sm:grid-cols-2 sm:gap-3">
                <button
                  type="button"
                  onClick={() => onAgree()}
                  className="inline-flex w-full justify-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-xs hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 sm:col-start-2"
                >
                  Agree
                </button>
                <button
                  type="button"
                  data-autofocus="true"
                  onClick={() => signOut()}
                  className="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-xs ring-1 ring-gray-300 ring-inset hover:bg-gray-50 sm:col-start-1 sm:mt-0"
                >
                  Cancel
                </button>
              </div>
            </DialogPanel>
          </div>
        </div>
      </Dialog>
    </>
  );
}
